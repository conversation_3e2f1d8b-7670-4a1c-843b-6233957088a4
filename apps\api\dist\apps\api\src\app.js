"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const database_1 = require("@freela/database");
const redis_1 = require("./utils/redis");
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
// Middleware imports
const security_1 = __importDefault(require("./middleware/security"));
const error_1 = require("./middleware/error");
// Route imports
const auth_1 = __importDefault(require("./routes/auth"));
const ai_1 = __importDefault(require("./routes/ai"));
const onboarding_1 = __importDefault(require("./routes/onboarding"));
const services_1 = __importDefault(require("./routes/services"));
const experts_1 = __importDefault(require("./routes/experts"));
const bookings_1 = __importDefault(require("./routes/bookings"));
const search_1 = __importDefault(require("./routes/search"));
class App {
    app;
    constructor() {
        this.app = (0, express_1.default)();
    }
    initializeMiddleware() {
        // Trust proxy for accurate IP addresses
        this.app.set('trust proxy', 1);
        // Security middleware
        this.app.use(security_1.default.requestId);
        this.app.use(security_1.default.securityHeaders);
        this.app.use(security_1.default.helmet);
        this.app.use(security_1.default.cors);
        this.app.use(security_1.default.compression);
        this.app.use(security_1.default.suspiciousActivityDetection);
        // Rate limiting
        this.app.use(security_1.default.rateLimit);
        // Request parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        // Request logging
        this.app.use(logger_1.requestLogger);
        // Health check endpoint (before rate limiting)
        this.app.get('/health', this.healthCheck);
    }
    initializeRoutes() {
        const apiVersion = config_1.config.API_VERSION;
        // API routes
        this.app.use(`/api/${apiVersion}/auth`, auth_1.default);
        this.app.use(`/api/${apiVersion}/ai`, ai_1.default);
        this.app.use(`/api/${apiVersion}/onboarding`, onboarding_1.default);
        this.app.use(`/api/${apiVersion}/services`, services_1.default);
        this.app.use(`/api/${apiVersion}/experts`, experts_1.default);
        this.app.use(`/api/${apiVersion}/bookings`, bookings_1.default);
        this.app.use(`/api/${apiVersion}/search`, search_1.default);
        // Simple test endpoint
        this.app.get(`/api/${apiVersion}/test-connection`, (req, res) => {
            res.json({
                success: true,
                message: 'API connection test successful',
                timestamp: new Date().toISOString(),
                supabase: {
                    url: process.env.SUPABASE_URL ? 'configured' : 'not configured',
                    anonKey: process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured'
                },
                openrouter: {
                    apiKey: process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured'
                }
            });
        });
        // Health check endpoint
        this.app.get('/health', this.healthCheck);
        // Root endpoint
        this.app.get('/', (req, res) => {
            res.json({
                success: true,
                message: 'Freela Syria API Server',
                version: apiVersion,
                timestamp: new Date().toISOString(),
                environment: config_1.config.NODE_ENV,
            });
        });
        // API info endpoint
        this.app.get(`/api/${apiVersion}`, (req, res) => {
            res.json({
                success: true,
                message: 'Freela Syria API',
                version: apiVersion,
                endpoints: {
                    test: `/api/${apiVersion}/test-connection`,
                    docs: `/api/${apiVersion}/docs`,
                    health: '/health',
                },
                timestamp: new Date().toISOString(),
            });
        });
    }
    initializeSwagger() {
        if (!config_1.isDevelopment)
            return;
        const swaggerOptions = {
            definition: {
                openapi: '3.0.0',
                info: {
                    title: 'Freela Syria API',
                    version: '1.0.0',
                    description: 'AI-Powered Freelance Marketplace API for Syrian Experts',
                    contact: {
                        name: 'Freela Syria Team',
                        email: '<EMAIL>',
                    },
                    license: {
                        name: 'MIT',
                        url: 'https://opensource.org/licenses/MIT',
                    },
                },
                servers: [
                    {
                        url: `http://localhost:${config_1.config.PORT}/api/${config_1.config.API_VERSION}`,
                        description: 'Development server',
                    },
                ],
                components: {
                    securitySchemes: {
                        bearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                        },
                    },
                },
                security: [
                    {
                        bearerAuth: [],
                    },
                ],
                tags: [
                    {
                        name: 'Authentication',
                        description: 'User authentication and authorization endpoints',
                    },
                    {
                        name: 'Users',
                        description: 'User management endpoints',
                    },
                    {
                        name: 'Experts',
                        description: 'Expert profile management endpoints',
                    },
                    {
                        name: 'Services',
                        description: 'Service management endpoints',
                    },
                    {
                        name: 'Bookings',
                        description: 'Booking management endpoints',
                    },
                    {
                        name: 'Payments',
                        description: 'Payment processing endpoints',
                    },
                    {
                        name: 'Chat',
                        description: 'Messaging and chat endpoints',
                    },
                    {
                        name: 'Admin',
                        description: 'Administrative endpoints',
                    },
                    {
                        name: 'AI Onboarding',
                        description: 'AI-powered onboarding and conversation endpoints',
                    },
                ],
            },
            apis: ['./src/routes/*.ts'], // Path to the API docs
        };
        const swaggerSpec = (0, swagger_jsdoc_1.default)(swaggerOptions);
        // Swagger UI
        this.app.use(`/api/${config_1.config.API_VERSION}/docs`, swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec, {
            explorer: true,
            customCss: '.swagger-ui .topbar { display: none }',
            customSiteTitle: 'Freela Syria API Documentation',
        }));
        // Swagger JSON
        this.app.get(`/api/${config_1.config.API_VERSION}/docs.json`, (req, res) => {
            res.setHeader('Content-Type', 'application/json');
            res.send(swaggerSpec);
        });
        logger_1.logger.info(`Swagger documentation available at http://localhost:${config_1.config.PORT}/api/${config_1.config.API_VERSION}/docs`);
    }
    initializeErrorHandling() {
        // 404 handler
        this.app.use(error_1.notFoundHandler);
        // Global error handler
        this.app.use(error_1.errorHandler);
    }
    async initialize() {
        logger_1.logger.info('Initializing application...');
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeSwagger();
        this.initializeErrorHandling();
        // Connect to external services with graceful degradation
        try {
            await (0, database_1.connectDatabase)();
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Database connection failed, continuing without database', { error });
        }
        // Try to connect to Redis, but don't fail if it's not available
        try {
            await redis_1.redis.connect();
            logger_1.logger.info('✅ Redis connected successfully');
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Redis connection failed, continuing without Redis', { error });
        }
        logger_1.logger.info('✅ Application initialized successfully.');
    }
    async shutdown() {
        logger_1.logger.info('Shutting down application...');
        await (0, database_1.disconnectDatabase)();
        await redis_1.redis.disconnect();
        logger_1.logger.info('✅ Application shutdown successfully.');
    }
    healthCheck = async (req, res) => {
        try {
            // Check database health with timeout
            let dbHealth = { status: 'unhealthy' };
            try {
                const dbHealthPromise = (0, database_1.checkDatabaseHealth)();
                const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Database health check timeout')), 5000));
                dbHealth = await Promise.race([dbHealthPromise, timeoutPromise]);
            }
            catch (error) {
                logger_1.logger.warn('Database health check failed', { error });
                dbHealth = { status: 'unhealthy' };
            }
            const redisHealth = redis_1.redis.isReady();
            const healthStatus = {
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: config_1.config.NODE_ENV,
                version: config_1.config.API_VERSION,
                services: {
                    database: {
                        status: dbHealth.status === 'healthy' ? 'ok' : 'error',
                    },
                    redis: {
                        status: redisHealth ? 'ok' : 'error',
                    },
                },
            };
            // Always return 200 for health check - services can be degraded
            res.status(200).json(healthStatus);
        }
        catch (error) {
            logger_1.logger.error('Health check failed', { error });
            res.status(200).json({
                status: 'error',
                timestamp: new Date().toISOString(),
                message: 'Health check failed',
            });
        }
    };
}
exports.default = App;
//# sourceMappingURL=app.js.map