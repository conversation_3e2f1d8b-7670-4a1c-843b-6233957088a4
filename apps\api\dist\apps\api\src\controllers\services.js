"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServicesByExpert = exports.deleteService = exports.updateService = exports.getServiceById = exports.searchServices = exports.createService = void 0;
const supabase_1 = require("@freela/database/src/supabase");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
// Service data mapping utilities
const mapToSupabaseService = (data) => {
    const mapped = {};
    for (const [key, value] of Object.entries(data)) {
        switch (key) {
            case 'expertId':
                mapped.expert_id = value;
                break;
            case 'priceType':
                mapped.price_type = value;
                break;
            case 'basePrice':
                mapped.base_price = value;
                break;
            case 'deliveryTime':
                mapped.delivery_time = value;
                break;
            case 'serviceType':
                mapped.service_type = value;
                break;
            case 'isActive':
                mapped.is_active = value;
                break;
            case 'createdAt':
                mapped.created_at = value;
                break;
            case 'updatedAt':
                mapped.updated_at = value;
                break;
            default:
                mapped[key] = value;
        }
    }
    return mapped;
};
const mapFromSupabaseService = (data) => {
    if (!data)
        return data;
    const mapped = {};
    for (const [key, value] of Object.entries(data)) {
        switch (key) {
            case 'expert_id':
                mapped.expertId = value;
                break;
            case 'price_type':
                mapped.priceType = value;
                break;
            case 'base_price':
                mapped.basePrice = value;
                break;
            case 'delivery_time':
                mapped.deliveryTime = value;
                break;
            case 'service_type':
                mapped.serviceType = value;
                break;
            case 'is_active':
                mapped.isActive = value;
                break;
            case 'created_at':
                mapped.createdAt = value;
                break;
            case 'updated_at':
                mapped.updatedAt = value;
                break;
            default:
                mapped[key] = value;
        }
    }
    return mapped;
};
/**
 * Create a new service
 */
const createService = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            throw new errors_1.AppError('User not authenticated', 401);
        }
        // Check if user has an expert profile
        const { data: expertProfile, error: expertError } = await supabase_1.supabaseAdmin
            .from('expert_profiles')
            .select('id')
            .eq('user_id', userId)
            .single();
        if (expertError || !expertProfile) {
            throw new errors_1.AppError('Expert profile not found. Please complete your expert profile first.', 400);
        }
        const serviceData = {
            ...req.body,
            expertId: expertProfile.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const supabaseData = mapToSupabaseService(serviceData);
        const { data: service, error } = await supabase_1.supabaseAdmin
            .from('services')
            .insert(supabaseData)
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
            .single();
        if (error) {
            logger_1.logger.error('Error creating service:', error);
            throw new errors_1.AppError('Failed to create service', 500);
        }
        const mappedService = mapFromSupabaseService(service);
        logger_1.logger.info('Service created successfully', {
            serviceId: service.id,
            expertId: expertProfile.id,
            userId
        });
        res.status(201).json({
            success: true,
            message: 'Service created successfully',
            data: mappedService
        });
    }
    catch (error) {
        logger_1.logger.error('Create service error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.createService = createService;
/**
 * Search and list services with filters and pagination
 */
const searchServices = async (req, res) => {
    try {
        const { q, category, subcategory, serviceType, priceType, minPrice, maxPrice, governorate, city, tags, sortBy = 'created_at', sortOrder = 'desc', page = 1, limit = 20, expertId, isActive = true } = req.query;
        let query = supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          total_reviews,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `, { count: 'exact' });
        // Apply filters
        if (isActive !== undefined) {
            query = query.eq('is_active', isActive);
        }
        if (category) {
            query = query.eq('category', category);
        }
        if (subcategory) {
            query = query.eq('subcategory', subcategory);
        }
        if (serviceType) {
            query = query.eq('service_type', serviceType);
        }
        if (priceType) {
            query = query.eq('price_type', priceType);
        }
        if (minPrice) {
            query = query.gte('base_price', minPrice);
        }
        if (maxPrice) {
            query = query.lte('base_price', maxPrice);
        }
        if (expertId) {
            query = query.eq('expert_id', expertId);
        }
        // Location filters for physical services
        if (governorate) {
            query = query.contains('location', { governorate });
        }
        if (city) {
            query = query.contains('location', { city });
        }
        // Text search
        if (q) {
            query = query.or(`title.ilike.%${q}%,description.ilike.%${q}%,category.ilike.%${q}%`);
        }
        // Tags search
        if (tags) {
            const tagArray = tags.toString().split(',').map(tag => tag.trim());
            query = query.overlaps('tags', tagArray);
        }
        // Sorting
        const sortField = sortBy === 'price' ? 'base_price' :
            sortBy === 'rating' ? 'expert_profiles.rating' :
                sortBy === 'popularity' ? 'expert_profiles.total_reviews' :
                    'created_at';
        query = query.order(sortField, { ascending: sortOrder === 'asc' });
        // Pagination
        const offset = (Number(page) - 1) * Number(limit);
        query = query.range(offset, offset + Number(limit) - 1);
        const { data: services, error, count } = await query;
        if (error) {
            logger_1.logger.error('Error searching services:', error);
            throw new errors_1.AppError('Failed to search services', 500);
        }
        const mappedServices = services?.map(mapFromSupabaseService) || [];
        const totalPages = Math.ceil((count || 0) / Number(limit));
        res.json({
            success: true,
            message: 'Services retrieved successfully',
            data: {
                services: mappedServices,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total: count || 0,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Search services error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.searchServices = searchServices;
/**
 * Get service by ID
 */
const getServiceById = async (req, res) => {
    try {
        const { id } = req.params;
        const { data: service, error } = await supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          total_reviews,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
            .eq('id', id)
            .single();
        if (error || !service) {
            throw new errors_1.AppError('Service not found', 404);
        }
        const mappedService = mapFromSupabaseService(service);
        res.json({
            success: true,
            message: 'Service retrieved successfully',
            data: mappedService
        });
    }
    catch (error) {
        logger_1.logger.error('Get service by ID error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.getServiceById = getServiceById;
/**
 * Update service
 */
const updateService = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        if (!userId) {
            throw new errors_1.AppError('User not authenticated', 401);
        }
        // Get the service to check ownership
        const { data: existingService, error: fetchError } = await supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id
        )
      `)
            .eq('id', id)
            .single();
        if (fetchError || !existingService) {
            throw new errors_1.AppError('Service not found', 404);
        }
        // Check if user owns the service or is admin
        if (userRole !== 'ADMIN' && existingService.expert_profiles.user_id !== userId) {
            throw new errors_1.AppError('Access denied - you can only update your own services', 403);
        }
        const updateData = {
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        const supabaseData = mapToSupabaseService(updateData);
        const { data: updatedService, error } = await supabase_1.supabaseAdmin
            .from('services')
            .update(supabaseData)
            .eq('id', id)
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
            .single();
        if (error) {
            logger_1.logger.error('Error updating service:', error);
            throw new errors_1.AppError('Failed to update service', 500);
        }
        const mappedService = mapFromSupabaseService(updatedService);
        logger_1.logger.info('Service updated successfully', {
            serviceId: id,
            userId
        });
        res.json({
            success: true,
            message: 'Service updated successfully',
            data: mappedService
        });
    }
    catch (error) {
        logger_1.logger.error('Update service error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.updateService = updateService;
/**
 * Delete service
 */
const deleteService = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        if (!userId) {
            throw new errors_1.AppError('User not authenticated', 401);
        }
        // Get the service to check ownership
        const { data: existingService, error: fetchError } = await supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id
        )
      `)
            .eq('id', id)
            .single();
        if (fetchError || !existingService) {
            throw new errors_1.AppError('Service not found', 404);
        }
        // Check if user owns the service or is admin
        if (userRole !== 'ADMIN' && existingService.expert_profiles.user_id !== userId) {
            throw new errors_1.AppError('Access denied - you can only delete your own services', 403);
        }
        // Check if service has active bookings
        const { data: activeBookings, error: bookingError } = await supabase_1.supabaseAdmin
            .from('bookings')
            .select('id')
            .eq('service_id', id)
            .in('status', ['PENDING', 'ACCEPTED', 'IN_PROGRESS']);
        if (bookingError) {
            logger_1.logger.error('Error checking active bookings:', bookingError);
        }
        if (activeBookings && activeBookings.length > 0) {
            throw new errors_1.AppError('Cannot delete service with active bookings. Please complete or cancel active bookings first.', 400);
        }
        const { error } = await supabase_1.supabaseAdmin
            .from('services')
            .delete()
            .eq('id', id);
        if (error) {
            logger_1.logger.error('Error deleting service:', error);
            throw new errors_1.AppError('Failed to delete service', 500);
        }
        logger_1.logger.info('Service deleted successfully', {
            serviceId: id,
            userId
        });
        res.json({
            success: true,
            message: 'Service deleted successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Delete service error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.deleteService = deleteService;
/**
 * Get services by expert ID
 */
const getServicesByExpert = async (req, res) => {
    try {
        const { expertId } = req.params;
        const { isActive, page = 1, limit = 20 } = req.query;
        let query = supabase_1.supabaseAdmin
            .from('services')
            .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          total_reviews,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `, { count: 'exact' })
            .eq('expert_id', expertId);
        // Apply active filter if specified
        if (isActive !== undefined) {
            query = query.eq('is_active', isActive);
        }
        // Pagination
        const offset = (Number(page) - 1) * Number(limit);
        query = query.range(offset, offset + Number(limit) - 1);
        // Order by creation date (newest first)
        query = query.order('created_at', { ascending: false });
        const { data: services, error, count } = await query;
        if (error) {
            logger_1.logger.error('Error getting services by expert:', error);
            throw new errors_1.AppError('Failed to retrieve expert services', 500);
        }
        const mappedServices = services?.map(mapFromSupabaseService) || [];
        const totalPages = Math.ceil((count || 0) / Number(limit));
        res.json({
            success: true,
            message: 'Expert services retrieved successfully',
            data: {
                services: mappedServices,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total: count || 0,
                    totalPages
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get services by expert error:', error);
        if (error instanceof errors_1.AppError) {
            return res.status(error.statusCode).json({
                success: false,
                message: error.message,
                type: error.type
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            type: 'INTERNAL_ERROR'
        });
    }
};
exports.getServicesByExpert = getServicesByExpert;
//# sourceMappingURL=services.js.map